<?php

namespace Tests\Feature;

use <PERSON><PERSON>ble\Ecommerce\Enums\MainTypeEnum;
use Botble\Ecommerce\Facades\EcommerceHelper;
use <PERSON><PERSON>ble\Ecommerce\Models\Product;
use Botble\Ecommerce\Repositories\Interfaces\ProductInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MainTypeFilterTest extends TestCase
{
    use RefreshDatabase;

    protected ProductInterface $productRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->productRepository = app(ProductInterface::class);
    }

    public function test_main_type_filter_is_enabled_by_default()
    {
        $this->assertTrue(EcommerceHelper::isEnabledFilterProductsByMainTypes());
    }

    public function test_main_types_for_filter_returns_empty_when_disabled()
    {
        // Mock the setting to return false
        config(['plugins.ecommerce.general.enable_filter_products_by_main_types' => false]);
        
        $mainTypes = EcommerceHelper::mainTypesForFilter();
        
        $this->assertTrue($mainTypes->isEmpty());
    }

    public function test_main_types_for_filter_returns_available_types_with_products()
    {
        // Create test products with different main types
        Product::create([
            'name' => 'Industrial Product 1',
            'main_type' => MainTypeEnum::INDUSTRIAL,
            'status' => 'published',
            'is_variation' => 0,
            'price' => 100,
            'sku' => 'IND-001',
        ]);

        Product::create([
            'name' => 'Industrial Product 2',
            'main_type' => MainTypeEnum::INDUSTRIAL,
            'status' => 'published',
            'is_variation' => 0,
            'price' => 200,
            'sku' => 'IND-002',
        ]);

        Product::create([
            'name' => 'Marine Product 1',
            'main_type' => MainTypeEnum::MARINE,
            'status' => 'published',
            'is_variation' => 0,
            'price' => 300,
            'sku' => 'MAR-001',
        ]);

        $mainTypes = EcommerceHelper::mainTypesForFilter();

        $this->assertCount(2, $mainTypes);
        
        $industrialType = $mainTypes->firstWhere('value', MainTypeEnum::INDUSTRIAL);
        $this->assertNotNull($industrialType);
        $this->assertEquals(2, $industrialType->products_count);
        
        $marineType = $mainTypes->firstWhere('value', MainTypeEnum::MARINE);
        $this->assertNotNull($marineType);
        $this->assertEquals(1, $marineType->products_count);
    }

    public function test_filter_products_by_main_types()
    {
        // Create test products with different main types
        $industrialProduct = Product::create([
            'name' => 'Industrial Product',
            'main_type' => MainTypeEnum::INDUSTRIAL,
            'status' => 'published',
            'is_variation' => 0,
            'price' => 100,
            'sku' => 'IND-TEST',
        ]);

        $marineProduct = Product::create([
            'name' => 'Marine Product',
            'main_type' => MainTypeEnum::MARINE,
            'status' => 'published',
            'is_variation' => 0,
            'price' => 200,
            'sku' => 'MAR-TEST',
        ]);

        $aerospaceProduct = Product::create([
            'name' => 'Aerospace Product',
            'main_type' => MainTypeEnum::AEROSPACE,
            'status' => 'published',
            'is_variation' => 0,
            'price' => 300,
            'sku' => 'AER-TEST',
        ]);

        // Test filtering by industrial main type
        $industrialProducts = $this->productRepository->filterProducts([
            'main_types' => [MainTypeEnum::INDUSTRIAL],
        ]);

        $this->assertCount(1, $industrialProducts);
        $this->assertEquals($industrialProduct->id, $industrialProducts->first()->id);

        // Test filtering by multiple main types
        $multipleProducts = $this->productRepository->filterProducts([
            'main_types' => [MainTypeEnum::MARINE, MainTypeEnum::AEROSPACE],
        ]);

        $this->assertCount(2, $multipleProducts);
        $productIds = $multipleProducts->pluck('id')->toArray();
        $this->assertContains($marineProduct->id, $productIds);
        $this->assertContains($aerospaceProduct->id, $productIds);
    }

    public function test_has_any_product_filters_includes_main_types()
    {
        // Disable all other filters
        config([
            'plugins.ecommerce.general.enable_filter_products_by_categories' => false,
            'plugins.ecommerce.general.enable_filter_products_by_brands' => false,
            'plugins.ecommerce.general.enable_filter_products_by_tags' => false,
            'plugins.ecommerce.general.enable_filter_products_by_attributes' => false,
            'plugins.ecommerce.general.enable_filter_products_by_price' => false,
            'plugins.ecommerce.general.enable_filter_products_by_main_types' => true,
        ]);

        $this->assertTrue(EcommerceHelper::hasAnyProductFilters());
    }
}
