<div class="ps-shopping ps-tab-root">
    <div class="bg-light py-2 mb-3">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12 col-md-6">
                    <div class="products-found pt-3">
                        <strong><?php echo e($products->total()); ?></strong><span class="ml-1"><?php echo e(__('Products found')); ?></span>
                    </div>
                </div>
                <div class="col-12 col-md-6">
                    <div class="d-flex justify-content-center justify-content-md-end">
                        <?php echo $__env->make(Theme::getThemeNamespace() . '::views/ecommerce/includes/sort', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <div class="ps-shopping__view ml-2">
                            <ul class="products-layout mb-0 p-0">
                                <li <?php if(request()->get('layout') != 'list'): ?> class="active" <?php endif; ?>><a href="#grid" data-layout="grid"><i class="icon-grid"></i></a></li>
                                <li <?php if(request()->get('layout') == 'list'): ?> class="active" <?php endif; ?>><a href="#list" data-layout="list"><i class="icon-list4"></i></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="ps-tabs ps-products-listing bb-product-items-wrapper">
        <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product-items'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php echo apply_filters('ecommerce_after_product_listing'); ?>

    </div>
</div>
<?php /**PATH D:\laragon\www\muhrak\platform\themes/muhrak/views/ecommerce/includes/products-list.blade.php ENDPATH**/ ?>