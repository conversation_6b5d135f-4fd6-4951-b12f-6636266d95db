<?php
    Theme::set('stickyHeader', 'false');
    Theme::set('topHeader', Theme::partial('header-product-page', compact('product')));
    Theme::set('bottomFooter', Theme::partial('footer-product-page', compact('product')));
    Theme::set('pageId', 'product-page');
    Theme::set('headerMobile', Theme::partial('header-mobile-product'));
?>

<div class="ps-page--product">
    <div class="container" id="app">
            <div class="ps-page__container">
                <div class="ps-page__left">
                    <div class="ps-product--detail ps-product--fullwidth">
                        <div class="ps-product__header">
                            <div class="ps-product__thumbnail" data-vertical="true">
                                <figure>
                                    <div class="ps-wrapper">
                                        <div class="ps-product__gallery" data-arrow="false">
                                            <?php $__currentLoopData = $productImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $img): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="item">
                                                    <a href="<?php echo e(RvMedia::getImageUrl($img)); ?>">
                                                        <img src="<?php echo e(RvMedia::getImageUrl($img)); ?>" alt="<?php echo e($product->name); ?>"/>
                                                    </a>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </figure>
                                <div class="ps-product__variants" data-item="4" data-md="4" data-sm="4" data-arrow="true">
                                    <?php $__currentLoopData = $productImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $img): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="item">
                                            <img src="<?php echo e(RvMedia::getImageUrl($img, 'thumb')); ?>" alt="<?php echo e($product->name); ?>"/>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                            <div class="ps-product__info">
                                <h1><?php echo e($product->name); ?></h1>
                                 <!-- Model Name Accordion -->
                                <div class="accordion-item-custom">
                                    <div class="info-row accordion-header" data-bs-toggle="collapse" data-bs-target="#modelNameCollapse" aria-expanded="false" style="cursor: pointer;">
                                        <div class="info-label">Model Name</div>
                                        <div class="info-value">
                                            <?php if($product->models->count()): ?>
                                                <?php echo e($product->models->first()->name); ?>

                                                <i class="fas fa-external-link-alt ms-1" style="font-size: 0.75rem;"></i>
                                            <?php endif; ?>
                                        </div>
                                        <i class="fas fa-chevron-down dropdown-icon ms-auto accordion-chevron"></i>
                                    </div>
                                    <div class="collapse" id="modelNameCollapse">
                                        <div class="accordion-content">
                                            <?php if($product->models->count()): ?>
                                                <?php $__currentLoopData = $product->models->skip(1); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $model): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="accordion-list-item">
                                                        <i class="fas fa-check-circle text-primary me-2"></i>
                                                        <a href="<?php echo e($model->url); ?>"><?php echo e($model->name); ?></a>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Series Accordion -->
                                <div class="accordion-item-custom">
                                    <div class="info-row accordion-header" data-bs-toggle="collapse" data-bs-target="#seriesCollapse" aria-expanded="false" style="cursor: pointer;">
                                        <div class="info-label">Series</div>
                                        <div class="info-value">
                                            <?php if($product->categories->count()): ?>
                                                <?php echo e($product->categories->first()->name); ?>

                                                <i class="fas fa-external-link-alt ms-1" style="font-size: 0.75rem;"></i>
                                            <?php endif; ?>
                                        </div>
                                        <i class="fas fa-chevron-down dropdown-icon ms-auto accordion-chevron"></i>
                                    </div>
                                    <div class="collapse" id="seriesCollapse">
                                        <div class="accordion-content">
                                            <?php if($product->categories->count()): ?>
                                                <?php $__currentLoopData = $product->categories->skip(1); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="accordion-list-item">
                                                        <i class="fas fa-check-circle text-primary me-2"></i>
                                                        <a href="<?php echo e($category->url); ?>"><?php echo e($category->name); ?></a>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Data Section Accordion -->
                                <div class="data-section">
                                    <div class="data-header accordion-header" data-bs-toggle="collapse" data-bs-target="#dataCollapse" aria-expanded="false" style="cursor: pointer;">
                                        <span>Data</span>
                                        <span>Catalog-Numalliance-CNC Wire Bender Robomac e-Motion</span>
                                        <i class="fas fa-chevron-down dropdown-icon accordion-chevron"></i>
                                    </div>
                                    <div class="collapse" id="dataCollapse">
                                        <div class="accordion-content">
                                            <div class="accordion-list-item">
                                                Catalog-Numalliance-CNC Wire Bender Robomac e-Motion (Current)
                                            </div>
                                            <div class="accordion-list-item">
                                                Technical Specifications Sheet
                                            </div>
                                            <div class="accordion-list-item">

                                                Product Images Gallery
                                            </div>
                                            <div class="accordion-list-item">
                                                Operation Manual Video
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php if(is_plugin_active('marketplace') && $product->store_id): ?>
                                <!-- Manufacturer Section -->
                                <div class="manufacturer-section">
                                    <h2 class="manufacturer-title">Manufacturer information</h2>

                                    <a href="<?php echo e($product->store->url); ?>" class="manufacturer-info">
                                        <div class="manufacturer-logo">
                                            <img src="<?php echo e(RvMedia::getImageUrl($product->store->logo, 'full', false, RvMedia::getDefaultImage())); ?>" alt="<?php echo e($product->store->name); ?>">
                                        </div>
                                        <div class="manufacturer-details">
                                            <h5>
                                                <?php echo e($product->store->name); ?>

                                                <i class="fas fa-check-circle verified-badge"></i>
                                                <i class="fas fa-chevron-right ms-1" style="color: #5f6368; font-size: 0.75rem;"></i>
                                            </h5>
                                            <p class="manufacturer-description">
                                                <?php echo BaseHelper::clean($product->store->description); ?>

                                            </p>
                                        </div>
                                    </a>

                                    <!-- Action Buttons -->
                                    <div class="action-buttons">
                                        <button class="btn btn-inquiry">
                                            <i class="fas fa-comment-dots me-2"></i>Inquiry
                                        </button>
                                        <button class="btn btn-order">
                                            How to order
                                        </button>
                                    </div>

                                    <!-- Problem Section -->
                                    <div class="problem-section">
                                        Problem with product info?
                                        <a href="#" class="problem-link">
                                            Update request <i class="fas fa-chevron-right ms-1"></i>
                                        </a>
                                    </div>

                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="ps-product__content ps-tab-root">
                                <div class="product-description-table">
                                    <?php echo BaseHelper::clean($product->description); ?>

                                </div>
                                <div class="ck-content">
                                    <?php echo BaseHelper::clean($product->content); ?>

                                </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="ps-section--default">
                <div class="ps-section__header">
                    <h3><?php echo e(__('Related products')); ?></h3>
                </div>
                <div class="ps-section__content">
                    <div class="ps-carousel--responsive owl-slider"
                         data-owl-auto="true"
                         data-owl-loop="false"
                         data-owl-speed="10000"
                         data-owl-gap="10"
                         data-owl-nav="false"
                         data-owl-dots="true"
                         data-owl-item="5"
                         data-owl-item-xs="2"
                         data-owl-item-sm="3"
                         data-owl-item-md="3"
                         data-owl-item-lg="4"
                         data-owl-item-xl="5"
                         data-owl-duration="1000"
                         data-owl-mousedrag="on"
                    >
                        <?php $__currentLoopData = $product->store->products()->take(7)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="ps-product">
                                <?php echo Theme::partial('product-item', ['product' => $product]); ?>

                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
    </div>
</div>
<?php /**PATH D:\laragon\www\muhrak\platform\themes/muhrak/views/ecommerce/product.blade.php ENDPATH**/ ?>