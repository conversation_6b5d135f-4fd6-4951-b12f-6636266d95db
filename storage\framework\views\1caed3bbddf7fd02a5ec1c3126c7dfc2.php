<?php if(EcommerceHelper::hasAnyProductFilters()): ?>
    <?php
        $dataForFilter = EcommerceHelper::dataForFilter($category ?? null, $request ?? null);
        [$categories, $brands, $tags, $rand, $categoriesRequest, $urlCurrent, $categoryId, $maxFilterPrice, $mainTypes] = $dataForFilter;
    ?>

    <div class="bb-shop-sidebar">
        <form action="<?php echo e(URL::current()); ?>" data-action="<?php echo e(route('public.products')); ?>" method="GET" class="bb-product-form-filter">
            <?php echo $__env->make(EcommerceHelper::viewPath('includes.filters.filter-hidden-fields'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            <?php echo apply_filters('theme_ecommerce_products_filter_before', null, $dataForFilter); ?>


            <?php if(EcommerceHelper::isEnabledFilterProductsByCategories()): ?>
                <?php echo $__env->make(EcommerceHelper::viewPath('includes.filters.categories'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>

            
            

            
            <?php if(EcommerceHelper::isEnabledFilterProductsByMainTypes()): ?>
                <?php echo $__env->make(EcommerceHelper::viewPath('includes.filters.main-types'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>

            <?php echo apply_filters('theme_ecommerce_products_filter_after', null, $dataForFilter); ?>

        </form>
    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\muhrak\platform/plugins/ecommerce/resources/views/themes/includes/filters.blade.php ENDPATH**/ ?>