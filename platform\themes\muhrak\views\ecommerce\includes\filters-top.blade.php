@if (EcommerceHelper::hasAnyProductFilters())
    @php
        $dataForFilter = EcommerceHelper::dataForFilter($category ?? null, $request ?? null);
        [$categories, $brands, $tags, $rand, $categoriesRequest, $urlCurrent, $categoryId, $maxFilterPrice, $mainTypes, $stores] = $dataForFilter;
    @endphp

    <div class="bb-product-filters-top">
        <div class="container">
            <form action="{{ URL::current() }}" data-action="{{ route('public.products') }}" method="GET" class="bb-product-form-filter">
                @include(EcommerceHelper::viewPath('includes.filters.filter-hidden-fields'))

                <div class="row g-3 align-items-end">
                    <!-- Categories Filter -->
                    @if (EcommerceHelper::isEnabledFilterProductsByCategories())
                        <div class="col-md-4">
                            <label class="form-label fw-bold">{{ __('Categories') }}</label>
                            <select name="categories[]" class="ps-filter-select form-select filter-select" multiple data-placeholder="{{ __('Select categories...') }}" >
                                @foreach ($categories as $category)
                                    <option value="{{ $category->id }}" 
                                        @selected(in_array($category->id, (array)request()->input('categories', [])))>
                                        {{ $category->name }} ({{ $category->products_count }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    @endif

                    <!-- Main Types Filter -->
                    @if (EcommerceHelper::isEnabledFilterProductsByMainTypes())
                        <div class="col-md-4">
                            <label class="form-label fw-bold">{{ __('Main Type') }}</label>
                            <select name="main_types[]" class="ps-filter-select form-select filter-select" multiple data-placeholder="{{ __('Select main types...') }}">
                                @foreach ($mainTypes as $mainType)
                                    <option value="{{ $mainType->value }}" 
                                        @selected(in_array($mainType->value, (array)request()->input('main_types', [])))>
                                        {{ $mainType->label }} ({{ $mainType->products_count }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    @endif

                    <!-- Stores Filter -->
                    @if (EcommerceHelper::isEnabledFilterProductsByStores())
                        <div class="col-md-4">
                            <label class="form-label fw-bold">{{ __('Stores') }}</label>
                            <select name="stores[]" class="ps-filter-select form-select filter-select" multiple data-placeholder="{{ __('Select stores...') }}">
                                @foreach ($stores as $store)
                                    <option value="{{ $store->id }}" 
                                        @selected(in_array($store->id, (array)request()->input('stores', [])))>
                                        {{ $store->name }} ({{ $store->products_count }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    @endif
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-1"></i>{{ __('Apply Filters') }}
                        </button>
                        <a href="{{ URL::current() }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>{{ __('Clear Filters') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

   
@endif
