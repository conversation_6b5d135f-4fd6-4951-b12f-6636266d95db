(function ($) {
    'use strict';

    let isRTL = $('body').prop('dir') === 'rtl'

    function backgroundImage() {
        let dataBackground = $('[data-background]');
        dataBackground.each(function () {
            if ($(this).attr('data-background')) {
                let imagePath = $(this).attr('data-background');
                $(this).css({
                    'background-image': 'url(' + imagePath + ')',
                    'background-color': '#fff'
                });
            }
        });
    }

    function siteToggleAction() {
        let navSidebar = $('.navigation--sidebar'),
            filterSidebar = $('.ps-filter--sidebar');
        $('.menu-toggle-open').on('click', function (e) {
            e.preventDefault();
            $(this).toggleClass('active');
            navSidebar.toggleClass('active');
            $('.ps-site-overlay').toggleClass('active');
        });

        $('.ps-toggle--sidebar').on('click', function (e) {
            e.preventDefault();
            let url = $(this).attr('href');
            $(this).toggleClass('active');
            $(this)
                .siblings('a')
                .removeClass('active');
            $(url).toggleClass('active');
            $(url)
                .siblings('.ps-panel--sidebar')
                .removeClass('active');
            if ($(this).hasClass('active')) {
                $('.ps-site-overlay').addClass('active');
            } else {
                $('.ps-site-overlay').removeClass('active');
            }
        });

        $('#filter-sidebar').on('click', function (e) {
            e.preventDefault();
            filterSidebar.addClass('active');
            $('.ps-site-overlay').addClass('active');
        });

        $('.ps-filter--sidebar .ps-filter__header .ps-btn--close').on(
            'click',
            function (e) {
                e.preventDefault();
                filterSidebar.removeClass('active');
                $('.ps-site-overlay').removeClass('active');
            }
        );

        $('body').on('click', function (e) {
            if (
                $(e.target)
                    .siblings('.ps-panel--sidebar')
                    .hasClass('active')
            ) {
                $('.ps-panel--sidebar').removeClass('active');
                $('.ps-site-overlay').removeClass('active');
            }
        });
    }

    function subMenuToggle() {
        $('.menu--mobile .menu-item-has-children > .sub-toggle').on(
            'click',
            function (e) {
                e.preventDefault();
                let current = $(this).parent('.menu-item-has-children');
                $(this).toggleClass('active');
                current
                    .siblings()
                    .find('.sub-toggle')
                    .removeClass('active');
                current.children('.sub-menu').slideToggle(350);
                current
                    .siblings()
                    .find('.sub-menu')
                    .slideUp(350);
                if (current.hasClass('has-mega-menu')) {
                    current.children('.mega-menu').slideToggle(350);
                    current
                        .siblings('.has-mega-menu')
                        .find('.mega-menu')
                        .slideUp(350);
                }
            }
        );
        $('.menu--mobile .has-mega-menu .mega-menu__column .sub-toggle').on(
            'click',
            function (e) {
                e.preventDefault();
                let current = $(this).closest('.mega-menu__column');
                $(this).toggleClass('active');
                current
                    .siblings()
                    .find('.sub-toggle')
                    .removeClass('active');
                current.children('.mega-menu__list').slideToggle(350);
                current
                    .siblings()
                    .find('.mega-menu__list')
                    .slideUp(350);
            }
        );

        let $listCategories = $(document).find('.widget-product-categories');
        if ($listCategories.length > 0) {
            $(document).on(
                'click',
                '.widget-product-categories .menu-item-has-children > .sub-toggle',
                function (e) {
                    e.preventDefault();
                    let current = $(this).parent('.menu-item-has-children');
                    $(this).toggleClass('active');
                    current
                        .siblings()
                        .find('.sub-toggle')
                        .removeClass('active');
                    current.children('.sub-menu').slideToggle(350);
                    current
                        .siblings()
                        .find('.sub-menu')
                        .slideUp(350);
                    if (current.hasClass('has-mega-menu')) {
                        current.children('.mega-menu').slideToggle(350);
                        current
                            .siblings('.has-mega-menu')
                            .find('.mega-menu')
                            .slideUp(350);
                    }
                }
            );
        }
    }

    function stickyHeader() {
        let header = $('.header'),
            checkpoint = 50;
        header.each(function () {
            if ($(this).data('sticky') === true) {
                let el = $(this);
                $(window).scroll(function () {
                    let currentPosition = $(this).scrollTop();
                    if (currentPosition > checkpoint) {
                        el.addClass('header--sticky');
                    } else {
                        el.removeClass('header--sticky');
                    }
                });
            }
        });

        let stickyCart = $('#cart-sticky');
        if (stickyCart.length > 0) {
            $(window).scroll(function () {
                let currentPosition = $(this).scrollTop();
                if (currentPosition > checkpoint) {
                    stickyCart.addClass('active');
                } else {
                    stickyCart.removeClass('active');
                }
            });
        }
    }

    function owlCarouselConfig() {
        let target = $('.owl-slider');
        if (target.length > 0) {
            target.each(function () {
                let el = $(this),
                    dataAuto = el.data('owl-auto'),
                    dataLoop = el.data('owl-loop'),
                    dataSpeed = el.data('owl-speed'),
                    dataGap = el.data('owl-gap'),
                    dataNav = el.data('owl-nav'),
                    dataDots = el.data('owl-dots'),
                    dataAnimateIn = el.data('owl-animate-in')
                        ? el.data('owl-animate-in')
                        : '',
                    dataAnimateOut = el.data('owl-animate-out')
                        ? el.data('owl-animate-out')
                        : '',
                    dataDefaultItem = el.data('owl-item'),
                    dataItemXS = el.data('owl-item-xs'),
                    dataItemSM = el.data('owl-item-sm'),
                    dataItemMD = el.data('owl-item-md'),
                    dataItemLG = el.data('owl-item-lg'),
                    dataItemXL = el.data('owl-item-xl'),
                    dataNavLeft = el.data('owl-nav-left')
                        ? el.data('owl-nav-left')
                        : "<i class='icon-chevron-left'></i>",
                    dataNavRight = el.data('owl-nav-right')
                        ? el.data('owl-nav-right')
                        : "<i class='icon-chevron-right'></i>",
                    duration = el.data('owl-duration'),
                    datamouseDrag =
                        el.data('owl-mousedrag') == 'on' ? true : false;
                if (
                    target.children('div, span, a, img, h1, h2, h3, h4, h5, h5')
                        .length >= 2
                ) {
                    el.addClass('owl-carousel').owlCarousel({
                        rtl: isRTL,
                        animateIn: dataAnimateIn,
                        animateOut: dataAnimateOut,
                        margin: dataGap,
                        autoplay: dataAuto,
                        autoplayTimeout: dataSpeed,
                        autoplayHoverPause: true,
                        loop: dataLoop,
                        nav: dataNav,
                        mouseDrag: datamouseDrag,
                        touchDrag: true,
                        autoplaySpeed: duration,
                        navSpeed: duration,
                        dotsSpeed: duration,
                        dragEndSpeed: duration,
                        navText: [dataNavLeft, dataNavRight],
                        dots: dataDots,
                        items: dataDefaultItem,
                        responsive: {
                            0: {
                                items: dataItemXS,
                            },
                            480: {
                                items: dataItemSM,
                            },
                            768: {
                                items: dataItemMD,
                            },
                            992: {
                                items: dataItemLG,
                            },
                            1200: {
                                items: dataItemXL,
                            },
                            1680: {
                                items: dataDefaultItem,
                            },
                        },
                    });
                }
            });
        }
    }

    function mapConfig() {
        let map = $('#contact-map');
        if (map.length > 0) {
            map.gmap3({
                address: map.data('address'),
                zoom: map.data('zoom'),
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                scrollwheel: false,
            })
                .marker(function (map) {
                    return {
                        position: map.getCenter(),
                        icon: 'img/marker.png',
                    };
                })
                .infowindow({
                    content: map.data('address'),
                })
                .then(function (infowindow) {
                    let map = this.get(0);
                    let marker = this.get(1);
                    marker.addListener('click', function () {
                        infowindow.open(map, marker);
                    });
                });
        } else {
            return false;
        }
    }

    function slickConfig() {
        let product = $('.ps-product--detail');
        if (product.length > 0) {
            let primary = product.find('.ps-product__gallery'),
                second = product.find('.ps-product__variants'),
                vertical = product
                    .find('.ps-product__thumbnail')
                    .data('vertical');
            primary.slick({
                slidesToShow: 1,
                slidesToScroll: 1,
                rtl: isRTL,
                asNavFor: '.ps-product__variants',
                fade: true,
                dots: false,
                infinite: false,
                arrows: primary.data('arrow'),
                prevArrow: "<button class='slick-prev slick-arrow'><i class='fa fa-angle-left'></i></button>",
                nextArrow: "<button class='slick-next slick-arrow'><i class='fa fa-angle-right'></i></button>",
            });
            second.slick({
                slidesToShow: second.data('item'),
                slidesToScroll: 1,
                rtl: isRTL,
                infinite: false,
                arrows: second.data('arrow'),
                focusOnSelect: true,
                prevArrow: "<button class='slick-prev slick-arrow'><i class='fa fa-angle-up'></i></button>",
                nextArrow: "<button class='slick-next slick-arrow'><i class='fa fa-angle-down'></i></button>",
                asNavFor: '.ps-product__gallery',
                vertical: vertical,
                responsive: [
                    {
                        breakpoint: 1200,
                        settings: {
                            arrows: second.data('arrow'),
                            slidesToShow: 4,
                            vertical: false,
                            prevArrow:
                                "<button class='slick-prev slick-arrow'><i class='fa fa-angle-left'></i></button>",
                            nextArrow:
                                "<button class='slick-next slick-arrow'><i class='fa fa-angle-right'></i></button>",
                        },
                    },
                    {
                        breakpoint: 992,
                        settings: {
                            arrows: second.data('arrow'),
                            slidesToShow: 4,
                            vertical: false,
                            prevArrow:
                                "<button class='slick-prev slick-arrow'><i class='fa fa-angle-left'></i></button>",
                            nextArrow:
                                "<button class='slick-next slick-arrow'><i class='fa fa-angle-right'></i></button>",
                        },
                    },
                    {
                        breakpoint: 480,
                        settings: {
                            slidesToShow: 3,
                            vertical: false,
                            prevArrow:
                                "<button class='slick-prev slick-arrow'><i class='fa fa-angle-left'></i></button>",
                            nextArrow:
                                "<button class='slick-next slick-arrow'><i class='fa fa-angle-right'></i></button>",
                        },
                    },
                ],
            });
        }
    }

    function tabs() {
        $('.ps-tab-list  li > a ').on('click', function (e) {
            e.preventDefault();
            let target = $(this).attr('href');
            $(this)
                .closest('li')
                .siblings('li')
                .removeClass('active');
            $(this)
                .closest('li')
                .addClass('active');
            $(target).addClass('active');
            $(target)
                .siblings('.ps-tab')
                .removeClass('active');
            $('.ps-tab-list li').removeClass('active');
            $('.ps-tab-list li a[href="' + target + '"]').closest('li').addClass('active');

            // $('html, body').animate(
            //     {
            //         scrollTop: ($(target).offset().top - $('.header--product .navigation').height() - 165) + 'px',
            //     },
            //     800
            // );
        });
        $('.ps-tab-list.owl-slider .owl-item a').on('click', function (e) {
            e.preventDefault();
            let target = $(this).attr('href');
            $(this)
                .closest('.owl-item')
                .siblings('.owl-item')
                .removeClass('active');
            $(this)
                .closest('.owl-item')
                .addClass('active');
            $(target).addClass('active');
            $(target)
                .siblings('.ps-tab')
                .removeClass('active');
        });
    }

    function rating() {
        $('select.ps-rating').each(function () {
            let readOnly;
            if ($(this).attr('data-read-only') == 'true') {
                readOnly = true;
            } else {
                readOnly = false;
            }
            $(this).barrating({
                theme: 'fontawesome-stars',
                readonly: readOnly,
                emptyValue: '0',
            });
        });
    }

    function productLightbox() {
        if (!$.fn.lightGallery) {
            return;
        }

        let product = $('.ps-product--detail');
        if (product.length > 0) {
            $('.ps-product__gallery').lightGallery({
                selector: '.item a',
                thumbnail: true,
                share: false,
                fullScreen: false,
                autoplay: false,
                autoplayControls: false,
                actualSize: false,
            });
            if (product.hasClass('ps-product--sticky')) {
                $('.ps-product__thumbnail').lightGallery({
                    selector: '.item a',
                    thumbnail: true,
                    share: false,
                    fullScreen: false,
                    autoplay: false,
                    autoplayControls: false,
                    actualSize: false,
                });
            }
        }
        if ($('.ps-gallery--image').length) {
            $('.ps-gallery--image').lightGallery({
                selector: '.ps-gallery__item',
                thumbnail: true,
                share: false,
                fullScreen: false,
                autoplay: false,
                autoplayControls: false,
                actualSize: false,
            });
        }

        if ($('.ps-video').length) {
            $('.ps-video').lightGallery({
                thumbnail: false,
                share: false,
                fullScreen: false,
                autoplay: false,
                autoplayControls: false,
                actualSize: false,
            });
        }
    }

    function backToTop() {
        let scrollPos = 0;
        let element = $('#back2top');
        $(window).scroll(function () {
            let scrollCur = $(window).scrollTop();
            if (scrollCur > scrollPos) {
                // scroll down
                if (scrollCur > 500) {
                    element.addClass('active');
                } else {
                    element.removeClass('active');
                }
            } else {
                // scroll up
                element.removeClass('active');
            }

            scrollPos = scrollCur;
        });

        element.on('click', function () {
            $('html, body').animate(
                {
                    scrollTop: '0px',
                },
                800
            );
        });
    }

    function modalInit() {
        let modal = $('.ps-modal');
        if (modal.length) {
            if (modal.hasClass('active')) {
                $('body').css('overflow-y', 'hidden');
            }
        }
        modal.find('.ps-modal__close, .ps-btn--close').on('click', function (e) {
            e.preventDefault();
            $(this)
                .closest('.ps-modal')
                .removeClass('active');
        });
        $('.ps-modal-link').on('click', function (e) {
            e.preventDefault();
            let target = $(this).attr('href');
            $(target).addClass('active');
            $('body').css('overflow-y', 'hidden');
        });
        $('.ps-modal').on('click', function (event) {
            if (!$(event.target).closest('.ps-modal__container').length) {
                modal.removeClass('active');
                $('body').css('overflow-y', 'auto');
            }
        });
    }

    function searchInit() {
        let searchbox = $('.ps-search');
        $('.ps-search-btn').on('click', function (e) {
            e.preventDefault();
            searchbox.addClass('active');
        });
        searchbox.find('.ps-btn--close').on('click', function (e) {
            e.preventDefault();
            searchbox.removeClass('active');
        });
    }

    function countDown() {
        let time = $('.ps-countdown');
        time.each(function () {
            let el = $(this),
                value = $(this).data('time');
            let countDownDate = new Date(value).getTime();
            let timeout = setInterval(function () {
                let now = new Date().getTime(),
                    distance = countDownDate - now;
                let days = Math.floor(distance / (1000 * 60 * 60 * 24)),
                    hours = Math.floor(
                        (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
                    ),
                    minutes = Math.floor(
                        (distance % (1000 * 60 * 60)) / (1000 * 60)
                    ),
                    seconds = Math.floor((distance % (1000 * 60)) / 1000);
                el.find('.days').html(days < 10 ? '0' + days : days);
                el.find('.hours').html(hours < 10 ? '0' + hours : hours);
                el.find('.minutes').html(minutes < 10 ? '0' + minutes : minutes);
                el.find('.seconds').html(seconds < 10 ? '0' + seconds : seconds);
                if (distance < 0) {
                    clearInterval(timeout);
                    el.closest('.ps-section').hide();
                }
            }, 1000);
        });
    }

    function productFilterToggle() {
        $('.ps-filter__trigger').on('click', function (e) {
            e.preventDefault();
            let el = $(this);
            el.find('.ps-filter__icon').toggleClass('active');
            el.closest('.ps-filter')
                .find('.ps-filter__content')
                .slideToggle();
        });
        if ($('.ps-sidebar--home').length > 0) {
            $('.ps-sidebar--home > .ps-sidebar__header > a').on(
                'click',
                function (e) {
                    e.preventDefault();
                    $(this)
                        .closest('.ps-sidebar--home')
                        .children('.ps-sidebar__content')
                        .slideToggle();
                }
            );
        }
    }

    function mainSlider() {
        let homeBanner = $('.ps-carousel--animate');
        homeBanner.slick({
            autoplay: true,
            rtl: isRTL,
            speed: 1000,
            lazyLoad: 'progressive',
            arrows: false,
            fade: true,
            dots: true,
            prevArrow: "<i class='slider-prev ba-back'></i>",
            nextArrow: "<i class='slider-next ba-next'></i>",
        });
    }

    function subscribePopup() {
        let subscribe = $('#subscribe'),
            time = subscribe.data('time');
        setTimeout(function () {
            if (subscribe.length > 0) {
                subscribe.addClass('active');
                $('body').css('overflow', 'hidden');
            }
        }, time);
        $('.ps-popup__close').on('click', function (e) {
            e.preventDefault();
            $(this)
                .closest('.ps-popup')
                .removeClass('active');
            $('body').css('overflow', 'auto');
        });
        $('#subscribe').on('click', function (event) {
            if (!$(event.target).closest('.ps-popup__content').length) {
                subscribe.removeClass('active');
                $('body').css('overflow-y', 'auto');
            }
        });
    }

    function stickySidebar() {
        let sticky = $('.ps-product--sticky'),
            stickySidebar,
            checkPoint = 992,
            windowWidth = $(window).innerWidth();
        if (sticky.length > 0) {
            stickySidebar = new StickySidebar(
                '.ps-product__sticky .ps-product__info',
                {
                    topSpacing: 20,
                    bottomSpacing: 20,
                    containerSelector: '.ps-product__sticky',
                }
            );
            if ($('.sticky-2').length > 0) {
                let stickySidebar2 = new StickySidebar(
                    '.ps-product__sticky .sticky-2',
                    {
                        topSpacing: 20,
                        bottomSpacing: 20,
                        containerSelector: '.ps-product__sticky',
                    }
                );
            }
            if (checkPoint > windowWidth) {
                stickySidebar.destroy();
                stickySidebar2.destroy();
            }
        } else {
            return false;
        }
    }

    function accordion() {
        let accordion = $('.ps-accordion');
        accordion.find('.ps-accordion__content').hide();
        $('.ps-accordion.active')
            .find('.ps-accordion__content')
            .show();
        accordion.find('.ps-accordion__header').on('click', function (e) {
            e.preventDefault();
            if (
                $(this)
                    .closest('.ps-accordion')
                    .hasClass('active')
            ) {
                $(this)
                    .closest('.ps-accordion')
                    .removeClass('active');
                $(this)
                    .closest('.ps-accordion')
                    .find('.ps-accordion__content')
                    .slideUp(350);
            } else {
                $(this)
                    .closest('.ps-accordion')
                    .addClass('active');
                $(this)
                    .closest('.ps-accordion')
                    .find('.ps-accordion__content')
                    .slideDown(350);
                $(this)
                    .closest('.ps-accordion')
                    .siblings('.ps-accordion')
                    .find('.ps-accordion__content')
                    .slideUp();
            }
            $(this)
                .closest('.ps-accordion')
                .siblings('.ps-accordion')
                .removeClass('active');
            $(this)
                .closest('.ps-accordion')
                .siblings('.ps-accordion')
                .find('.ps-accordion__content')
                .slideUp();
        });
    }

    function progressBar() {
        let progress = $('.ps-progress');
        progress.each(function () {
            let value = $(this).data('value');
            $(this)
                .find('span')
                .css({
                    width: value + '%',
                });
        });
    }

    function select2Config() {
        $('select.ps-select').select2({
            placeholder: $(this).data('placeholder'),
            minimumResultsForSearch: -1,
            templateSelection: function (state) {
                return jQuery.trim(state.text);
            }
        });

        $('select.ps-filter-select').select2({
            placeholder: function() {
                    return $(this).data('placeholder');
                },
            search: true,
            allowClear: true,
            closeOnSelect: false,
            minimumInputLength: 0,
            minimumResultsForSearch: -1,
           templateSelection: function(item) {
                return item.text || item.id;
            }
        });
    }

    function carouselNavigation() {
        let prevBtn = $('.ps-carousel__prev'),
            nextBtn = $('.ps-carousel__next');
        prevBtn.on('click', function (e) {
            e.preventDefault();
            let target = $(this).attr('href');
            $(target).trigger('prev.owl.carousel', [1000]);
        });
        nextBtn.on('click', function (e) {
            e.preventDefault();
            let target = $(this).attr('href');
            $(target).trigger('next.owl.carousel', [1000]);
        });
    }

    function filterSlider() {
        let nonLinearSlider = document.getElementById('nonlinear');
        if (typeof nonLinearSlider != 'undefined' && nonLinearSlider != null) {
            noUiSlider.create(nonLinearSlider, {
                connect: true,
                behaviour: 'tap',
                start: [0, 1000],
                range: {
                    min: 0,
                    '10%': 100,
                    '20%': 200,
                    '30%': 300,
                    '40%': 400,
                    '50%': 500,
                    '60%': 600,
                    '70%': 700,
                    '80%': 800,
                    '90%': 900,
                    max: 1000,
                },
            });
            let nodes = [
                document.querySelector('.ps-slider__min'),
                document.querySelector('.ps-slider__max'),
            ];
            nonLinearSlider.noUiSlider.on('update', function (values, handle) {
                nodes[handle].innerHTML = Math.round(values[handle]);
            });
        }
    }

    function handleLiveSearch() {
        $('body').on('click', function (e) {
            if (
                $(e.target).closest('.ps-form--search-header') ||
                e.target.className === '.ps-form--search-header'
            ) {
                $('.ps-panel--search-result').removeClass('active');
            }
        });
    }

    const reviewList = function () {
        let $reviewListWrapper = $('body').find('.comment-list');
        const $loadingSpinner = $('body').find('.loading-spinner');

        $loadingSpinner.addClass('d-none');

        const fetchData = (url, hasAnimation = false) => {
            $.ajax({
                url: url,
                type: 'GET',
                beforeSend: function () {
                    $loadingSpinner.removeClass('d-none');

                    if (hasAnimation) {
                        $('html, body').animate({
                            scrollTop: `${$('.product-reviews-container').offset().top}px`,
                        }, 1500);
                    }
                },
                success: function (res) {
                    $reviewListWrapper.html(res.data);
                    $('.product-reviews-container .product-reviews-header').html(res.message);

                    let $galleries = $('.product-reviews-container .block__images');
                    if ($galleries.length) {
                        $galleries.map((index, value) => {
                            if (!$(value).data('lightGallery')) {
                                $(value).lightGallery({
                                    selector: 'a',
                                    thumbnail: true,
                                    share: false,
                                    fullScreen: false,
                                    autoplay: false,
                                    autoplayControls: false,
                                    actualSize: false,
                                });
                            }
                        });
                    }
                }, complete: function () {
                    $loadingSpinner.addClass('d-none');
                }
            })
        }

        if ($reviewListWrapper.length < 1) {
            return;
        }

        fetchData($reviewListWrapper.data('url'));

        $reviewListWrapper.on('click', '.ps-pagination ul li.page-item a', function (e) {
            e.preventDefault()

            const href = $(this).attr('href')

            if (href === '#') {
                return
            }

            fetchData(href, true)
        })
    }

    function testimonialSlider() {
        $(".testimonial-carousel").owlCarousel({
            center: true,
            loop: true,
            margin: 20,
            autoplay: true,
            autoplayTimeout: 3000,
            autoplayHoverPause: true,
            nav: false,
            dots: true,
            // navText: ['<', '>'],
            responsive: {
                0: {
                    items: 1
                },
                768: {
                    items: 3
                },
                992: {
                    items: 3
                }
            }
        });
    }

    function clientLogoSlider() {
        $(".client-logo-carousel").owlCarousel({
            loop: true,
            margin: 10,
            autoplay: true,
            autoplayTimeout: 3000,
            autoplayHoverPause: true,
            nav: false,
            dots: false,
            // navText: ['<', '>'],
            responsive: {
                0: {
                    items: 2
                },
                768: {
                    items: 3
                },
                992: {
                    items: 6
                }
            }
        });
    }

    function cleanFormData(formDataInput) {
        const formData = formDataInput.filter((item) => item.value !== '' && (item.name !== 'per_page' || (item.name === 'per_page' && parseInt(item.value) !== 12)))

        let queryString = formData
            .filter((item) => item.name !== '_token')
            .map((item) => `${encodeURIComponent(item.name)}=${encodeURIComponent(item.value)}`)

        queryString = queryString.length > 0 ? `?${queryString.join('&')}` : ''

        return {
            formData: formData,
            queryString: queryString,
        }
    }

    function updatePaginationLinks() {
        $('.distributor-pagination a').each(function () {
            var oldHref = $(this).attr('href');
            console.log('oldHref', oldHref);

            if (oldHref) {
                var urlParams = new URLSearchParams(window.location.search);
                var oldParams = new URLSearchParams(oldHref.split('?')[1]);

                console.log('urlParams', urlParams)
                if (oldParams.has('page')) {
                    urlParams.set('page', oldParams.get('page'));
                }

                var newHref = window.location.pathname + '?' + urlParams.toString();
                $(this).attr('href', newHref);
            }
        });
    }

    const initMap = (formData) => {
        const $element = $('[data-bb-toggle="list-map"]')

        if ($element.length < 1) {
            return
        }

        if (window.activeMap) {
            window.activeMap.remove()
        }

        let center = $element.data('center')

        // const centerFirst = $('.homeya-box[data-lat][data-lng]').filter(
        //     (index, item) => $(item).data('lat') && $(item).data('lng')
        // )

        // if (centerFirst && centerFirst.length) {
        //     center = [centerFirst.data('lat'), centerFirst.data('lng')]
        // }

        const map = L.map($element.prop('id'), {
            attributionControl: false,
        }).setView(center, 14)

        L.tileLayer($element.data('tile-layer'), {
            maxZoom: $element.data('max-zoom') || 22,
        }).addTo(map)

        let totalPage = 0
        let currentPage = 1
        const markers = L.markerClusterGroup()

        const populate = () => {
            if (typeof formData === 'undefined') {
                const urlParams = new URLSearchParams(window.location.search)

                formData = {}

                if (urlParams.size > 0) {
                    for (const [key, value] of urlParams) {
                        formData[key] = value
                    }
                } else {
                    formData = {
                        page: 1,
                    }
                }
            } else if (Array.isArray(formData)) {
                formData = formData.reduce((acc, { name, value }) => {
                    acc[name] = value

                    return acc
                }, {})
            }

            formData.page = currentPage

            if (totalPage === 0 || currentPage <= totalPage) {
                $.ajax({
                    url: $element.data('url'),
                    type: 'GET',
                    data: formData,
                    success: ({ data, meta }) => {
                        if (data.length < 1) {
                            showNoRecordsMessage(map)
                            return
                        }
                        if (map.noRecordsControl) {
                            map.removeControl(map.noRecordsControl)
                            map.noRecordsControl = null
                        }

                        data.forEach((item) => {
                            if (!item.latitude || !item.longitude) {
                                return
                            }


                            let content = $('#distributor-map-content').html();


                            content = content
                                .replace(new RegExp('__name__', 'gi'), item.name)
                                .replace(new RegExp('__url__', 'gi'), item.website)
                                .replace(new RegExp('__address__', 'gi'), item.address)
                                .replace(new RegExp('__phone__', 'gi'), item.phone)
                                .replace(new RegExp('__email__', 'gi'), item.email)
                                .replace(new RegExp('__image__', 'gi'), item.logo_thumb)
                                .replace(new RegExp('__website__', 'gi'), item.website)
                                .replace(new RegExp('__country__', 'gi'), item.country)
                                .replace(new RegExp('__category_icon__', 'gi'), item.category_icon)
                                .replace(new RegExp('__category__', 'gi'), item.category)
                                .replace(new RegExp('__brand_logo__', 'gi'), item.brand_logo)
                                .replace(new RegExp('__brand__', 'gi'), item.brand)


                            const marker = L.marker(L.latLng(item.latitude, item.longitude), {
                                icon: L.divIcon({
                                    iconSize: L.point(30, 20),
                                    className: 'boxmarker',
                                    html: item.map_icon,
                                }),
                            })
                                .bindPopup(content, { maxWidth: '100%' })
                                .addTo(map)

                            markers.addLayer(marker)

                            // map.flyToBounds(markers.getBounds())
                            map.flyToBounds(markers.getBounds(), {
                                duration: 0.3 // default 1 second
                            });
                        })

                        if (totalPage === 0) {
                            totalPage = meta.last_page
                        }
                        currentPage++
                        populate()
                    },
                })
            }

        }

        populate()

        map.addLayer(markers)

        window.activeMap = map
    }
    function showNoRecordsMessage(map) {
        // Remove any existing control first
        if (map.noRecordsControl) {
            map.removeControl(map.noRecordsControl)
        }

        // Create new control
        const noRecordsControl = L.control({ position: 'topright' });

        noRecordsControl.onAdd = function () {
            const div = L.DomUtil.create('div', 'no-records-message')
            div.innerHTML = '<div style="padding: 10px;background: #ffd5d5;border: 1px solid #ff0000;color: red;font-size: 18px;">No records found</div>'
            return div
        }

        noRecordsControl.addTo(map)

        // Store reference so we can remove it later
        map.noRecordsControl = noRecordsControl
    }

    initMap()

    document.addEventListener('DOMContentLoaded', function () {
        const carousel = document.getElementById('categoryCarousel');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const categoryItems = document.querySelectorAll('.category-item');

        // Set initial scroll position
        let scrollPosition = 0;

        // Scroll distance for each click
        const scrollDistance = 300;

        // Scroll carousel to the left
        prevBtn.addEventListener('click', function () {
            scrollPosition = Math.max(scrollPosition - scrollDistance, 0);
            carousel.scrollTo({
                left: scrollPosition,
                behavior: 'smooth'
            });
            updateNavButtons();
        });

        // Scroll carousel to the right
        nextBtn.addEventListener('click', function () {
            scrollPosition = Math.min(scrollPosition + scrollDistance, carousel.scrollWidth - carousel.clientWidth);
            carousel.scrollTo({
                left: scrollPosition,
                behavior: 'smooth'
            });
            updateNavButtons();
        });

        // Update navigation button states
        function updateNavButtons() {
            prevBtn.disabled = scrollPosition <= 0;
            nextBtn.disabled = scrollPosition >= carousel.scrollWidth - carousel.clientWidth;

            prevBtn.style.opacity = prevBtn.disabled ? '0.5' : '1';
            nextBtn.style.opacity = nextBtn.disabled ? '0.5' : '1';
        }

        // Handle category selection
        categoryItems.forEach(item => {
            item.addEventListener('click', function () {
                // Remove active class from all items
                categoryItems.forEach(item => item.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // You can add logic here to filter your content based on selected category
                const selectedCategory = this.querySelector('.category-name').textContent;
                console.log('Selected category:', selectedCategory);

                // Example: trigger an event for Laravel to handle
                const event = new CustomEvent('categorySelected', {
                    detail: { category: selectedCategory }
                });
                document.dispatchEvent(event);
            });
        });

        // Update button states on manual scroll
        carousel.addEventListener('scroll', function () {
            scrollPosition = carousel.scrollLeft;
            updateNavButtons();
        });

        // Initialize button states
        updateNavButtons();
    });

    $(document).on('change', '.filter-form select[name="country"], .filter-form select[name="brand_id"], .filter-form input[name="category_id"], .filter-form select[name="per_page"]', (e) => {
        $(e.currentTarget).closest('form').trigger('submit');
    })
        .on('keyup', '.filter-form input[name="keyword"]', (e) => {
            if (e.code === 'Enter') {
                $(e.currentTarget).closest('form').trigger('submit')
            }
        })
        .on('click', '.filter-form .flat-pagination a', (e) => {
            e.preventDefault()

            const url = new URL(e.currentTarget.href)
            const $form = $(e.currentTarget).closest('form')

            $form.find('input[name="page"]').val(url.searchParams.get('page'))
            $form.trigger('submit')
        })
        .on('submit', '.filter-form', (e) => {
            e.preventDefault()
            const $dataListing = $('[data-bb-toggle="data-distributor-listing"]')
            const $form = $(e.currentTarget)
            const cleanedFormData = cleanFormData($form.serializeArray())
            const nextHref = $form.prop('action') + cleanedFormData.queryString

            $.ajax({
                url: $form.data('url') || $form.prop('action'),
                type: 'POST',
                data: cleanedFormData.formData,
                beforeSend: () => {
                    $dataListing.append('<div class="loading-spinner"></div>')
                },
                success: function ({ error, data, message }) {
                    if (error) {
                        Theme.showError(message)
                        return
                    }

                    $dataListing.html(data)

                    if (typeof Theme.lazyLoadInstance !== 'undefined') {
                        Theme.lazyLoadInstance.update()
                    }

                    initMap(cleanedFormData.formData)

                    if (nextHref !== window.location.href) {
                        window.history.pushState(cleanedFormData.formData, message, nextHref)

                        $('.reset-filter-btn').show()
                    }
                },
                complete: () => {
                    //$dataListing.find('.loading-spinner').remove();
                    var foundProperties = $('#found-listings').val();
                    $('#total-record-found').text(foundProperties);
                    updatePaginationLinks();
                },
            })
        })

    $(function () {
        backgroundImage();
        owlCarouselConfig();
        siteToggleAction();
        subMenuToggle();
        productFilterToggle();
        tabs();
        slickConfig();
        productLightbox();
        rating();
        backToTop();
        stickyHeader();
        mapConfig();
        modalInit();
        searchInit();
        countDown();
        mainSlider();
        stickySidebar();
        accordion();
        progressBar();
        select2Config();
        carouselNavigation();
        filterSlider();
        handleLiveSearch();
        reviewList();
        testimonialSlider();
        clientLogoSlider();
    });

    // $('[data-toggle="tooltip"]').tooltip();

    $('#product-quickview').on('shown.bs.modal', function () {
        $('.ps-product--quickview .ps-product__images').slick('setPosition');
    });

    $(window).on('load', function () {
        $('body').addClass('loaded');
    });

    let collapseBreadcrumb = function () {
        $('ul.breadcrumb li').each(function () {
            let $this = $(this);
            if (!$this.is(':first-child') && !$this.is(':nth-child(2)') && !$this.is(':last-child')) {
                if (!$this.is(':nth-child(3)')) {
                    $this.find('a').closest('li').hide();
                } else {
                    $this.find('a').hide();
                    $this.find('.extra-breadcrumb-name').text('...').show();
                }
            }
        });
    }

    if ($(window).width() < 768) {
        collapseBreadcrumb();
    }

    $(window).on('resize', function () {
        collapseBreadcrumb();
    });

    setTimeout(function () {
        if (!$('.mega-menu-wrapper').length) {
            return
        }

        if ($(window).width() > 1200 && typeof $.fn.masonry !== 'undefined') {
            $('.mega-menu-wrapper').masonry({
                // options...
                itemSelector: '.mega-menu__column',
                columnWidth: 200
            })
        }
    }, 500)


    let imgContainer = document.querySelector(".hero-img-container");

    if (imgContainer) {
        setInterval(() => {
            let last = imgContainer.firstElementChild;
            last.remove();
            imgContainer.appendChild(last);
        }, 2500);

    }


    // Get elements
    const $toggleButton = $('.store-category-list-toggle-button');
    const $categoryListRoot = $('.store-category-list-root');
    const $categoryList = $('.store-category-list');

    // Calculate the full height of the category list
    let fullHeight = $categoryList.outerHeight(true);
    let isExpanded = false;

    // Set initial state
    // $toggleButton.find('span').html('<svg focusable="false" aria-hidden="true" viewBox="0 0 512 512"><path d="M240 352V272H160C151.2 272 144 264.8 144 256C144 247.2 151.2 240 160 240H240V160C240 151.2 247.2 144 256 144C264.8 144 272 151.2 272 160V240H352C360.8 240 368 247.2 368 256C368 264.8 360.8 272 352 272H272V352C272 360.8 264.8 368 256 368C247.2 368 240 360.8 240 352zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 32C132.3 32 32 132.3 32 256C32 379.7 132.3 480 256 480C379.7 480 480 379.7 480 256C480 132.3 379.7 32 256 32z"></path></svg>');
    // $toggleButton.append(' View more');
    // $toggleButton.addClass('store-category-more-button');

    // Handle button click
    $toggleButton.click(function () {
        if (!isExpanded) {
            // Expand the list
            $categoryListRoot.animate({
                height: fullHeight
            }, 0);

            // Change button to "View less" state
            $toggleButton.html('<span><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-wr8ge5" focusable="false" aria-hidden="true" viewBox="0 0 512 512"><path d="M352 240C360.8 240 368 247.2 368 256C368 264.8 360.8 272 352 272H160C151.2 272 144 264.8 144 256C144 247.2 151.2 240 160 240H352zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 32C132.3 32 32 132.3 32 256C32 379.7 132.3 480 256 480C379.7 480 480 379.7 480 256C480 132.3 379.7 32 256 32z"></path></svg></span> View less');
            $toggleButton.removeClass('store-category-more-button').addClass('store-category-less-button');
            $toggleButton.css('position', 'relative');

        } else {
            // Collapse the list
            $categoryListRoot.animate({
                height: '46px'
            }, 0);

            // Change button back to "View more" state
            $toggleButton.html('<span><svg focusable="false" aria-hidden="true" viewBox="0 0 512 512"><path d="M240 352V272H160C151.2 272 144 264.8 144 256C144 247.2 151.2 240 160 240H240V160C240 151.2 247.2 144 256 144C264.8 144 272 151.2 272 160V240H352C360.8 240 368 247.2 368 256C368 264.8 360.8 272 352 272H272V352C272 360.8 264.8 368 256 368C247.2 368 240 360.8 240 352zM512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256zM256 32C132.3 32 32 132.3 32 256C32 379.7 132.3 480 256 480C379.7 480 480 379.7 480 256C480 132.3 379.7 32 256 32z"></path></svg></span> View more');
            $toggleButton.removeClass('store-category-less-button').addClass('store-category-more-button');
            $toggleButton.css('position', 'absolute');
        }
        // Toggle the state
        isExpanded = !isExpanded;
    });


})(jQuery);
