<?php if($mainTypes->isNotEmpty()): ?>
    <div class="bb-product-filter">
        <h4 class="bb-product-filter-title"><?php echo e(__('Main Type')); ?></h4>

        <div class="bb-product-filter-content">
            <ul class="bb-product-filter-items filter-checkbox">
                <?php $__currentLoopData = $mainTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mainType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="bb-product-filter-item">
                        <input id="attribute-main-type-<?php echo e($mainType->value); ?>" type="checkbox" name="main_types[]" value="<?php echo e($mainType->value); ?>" <?php if(in_array($mainType->value, (array)request()->input('main_types', []))): echo 'checked'; endif; ?> />
                        <label for="attribute-main-type-<?php echo e($mainType->value); ?>"><?php echo e($mainType->label); ?> (<?php echo e($mainType->products_count); ?>)</label>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\muhrak\platform/plugins/ecommerce/resources/views/themes/includes/filters/main-types.blade.php ENDPATH**/ ?>