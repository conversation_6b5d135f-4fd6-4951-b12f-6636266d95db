<?php
    $url = $data['url'];
    $width = $data['width'] ?? null;
    $height = $data['height'] ?? null;
?>

<?php switch($type):
    case ('youtube'): ?>
    <?php case ('vimeo'): ?>
        <div
            <?php if(! $width && ! $height): ?>
                style="position: relative; display: block; height: 0; padding-bottom: 56.25%; overflow: hidden; margin-bottom: 20px;"
            <?php else: ?>
                style="margin-bottom: 20px;"
            <?php endif; ?>
        >
            <iframe
                src="<?php echo e($url); ?>"
                <?php if(! $width && ! $height): ?>
                    style="position: absolute; top: 0; bottom: 0; left: 0; width: 100%; height: 100%; border: 0;"
                <?php endif; ?>
                allowfullscreen
                frameborder="0"
                <?php if($height): ?>
                    height="<?php echo e($height); ?>"
                <?php endif; ?>

                <?php if($width): ?>
                    width="<?php echo e($width); ?>"
                <?php endif; ?>

                title="Video"
            ></iframe>
        </div>
        <?php break; ?>
    <?php case ('tiktok'): ?>
        <blockquote
            class="tiktok-embed"
            cite="<?php echo e($data['url']); ?>"
            data-video-id="<?php echo e($data['video_id']); ?>"
            style="max-width: 605px; min-width: 325px; margin-bottom: 20px; border: none !important;">
            <section></section>
        </blockquote>
        <?php break; ?>
    <?php case ('twitter'): ?>
        <div style="margin-bottom: 20px; !important; display: flex; justify-content: center">
            <blockquote class="twitter-tweet" style="border: none !important;"><a href="<?php echo e($data['url']); ?>"></a></blockquote>
        </div>
        <?php break; ?>
    <?php case ('video'): ?>
        <video <?php if($width): ?> width="<?php echo e($width); ?>" <?php endif; ?> <?php if($height): ?> height="<?php echo e($height); ?>" <?php endif; ?> controls>
            <source src="<?php echo e($data['url']); ?>" type="video/<?php echo e($data['extension']); ?>">
        </video>
<?php endswitch; ?>
<?php /**PATH D:\laragon\www\muhrak\platform/packages/theme/resources/views/shortcodes/media.blade.php ENDPATH**/ ?>