<?php if(($attributes = $attributes->where('attribute_set_id', $set->id)) && $attributes->isNotEmpty()): ?>
    <div class="bb-product-filter-attribute-item">
        <h4 class="bb-product-filter-title"><?php echo e($set->title); ?></h4>

        <div class="bb-product-filter-content">
            <div class="widget-content ps-custom-scrollbar">
                <div class="attribute-values">
                    <ul class="text-swatch">
                        <?php $__currentLoopData = $attributes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attribute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li data-slug="<?php echo e($attribute->slug); ?>">
                                <div>
                                    <label>
                                        <input class="product-filter-item" type="checkbox" name="attributes[<?php echo e($set->slug); ?>][]" value="<?php echo e($attribute->id); ?>" <?php echo e(in_array($attribute->id, $selected) ? 'checked' : ''); ?>>
                                        <span><?php echo e($attribute->title); ?></span>
                                    </label>
                                </div>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\muhrak\platform\themes/muhrak/views/ecommerce/attributes/_layouts-filter/text.blade.php ENDPATH**/ ?>