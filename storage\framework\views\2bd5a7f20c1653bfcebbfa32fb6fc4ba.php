<?php if(EcommerceHelper::hasAnyProductFilters()): ?>
    <?php
        $dataForFilter = EcommerceHelper::dataForFilter($category ?? null, $request ?? null);
        [$categories, $brands, $tags, $rand, $categoriesRequest, $urlCurrent, $categoryId, $maxFilterPrice, $mainTypes, $stores] = $dataForFilter;
    ?>

    <div class="bb-product-filters-top">
        <div class="container-fluid">
            <form action="<?php echo e(URL::current()); ?>" data-action="<?php echo e(route('public.products')); ?>" method="GET" class="bb-product-form-filter">
                <?php echo $__env->make(EcommerceHelper::viewPath('includes.filters.filter-hidden-fields'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <div class="row g-3 align-items-end">
                    <!-- Categories Filter -->
                    <?php if(EcommerceHelper::isEnabledFilterProductsByCategories()): ?>
                        <div class="col-md-4">
                            <label class="form-label fw-bold"><?php echo e(__('Categories')); ?></label>
                            <select name="categories[]" class="form-select filter-select" multiple data-placeholder="<?php echo e(__('Select categories...')); ?>">
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id); ?>" 
                                        <?php if(in_array($category->id, (array)request()->input('categories', []))): echo 'selected'; endif; ?>>
                                        <?php echo e($category->name); ?> (<?php echo e($category->products_count); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    <?php endif; ?>

                    <!-- Main Types Filter -->
                    <?php if(EcommerceHelper::isEnabledFilterProductsByMainTypes()): ?>
                        <div class="col-md-4">
                            <label class="form-label fw-bold"><?php echo e(__('Main Type')); ?></label>
                            <select name="main_types[]" class="form-select filter-select" multiple data-placeholder="<?php echo e(__('Select main types...')); ?>">
                                <?php $__currentLoopData = $mainTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mainType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($mainType->value); ?>" 
                                        <?php if(in_array($mainType->value, (array)request()->input('main_types', []))): echo 'selected'; endif; ?>>
                                        <?php echo e($mainType->label); ?> (<?php echo e($mainType->products_count); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    <?php endif; ?>

                    <!-- Stores Filter -->
                    <?php if(EcommerceHelper::isEnabledFilterProductsByStores()): ?>
                        <div class="col-md-4">
                            <label class="form-label fw-bold"><?php echo e(__('Stores')); ?></label>
                            <select name="stores[]" class="form-select filter-select" multiple data-placeholder="<?php echo e(__('Select stores...')); ?>">
                                <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($store->id); ?>" 
                                        <?php if(in_array($store->id, (array)request()->input('stores', []))): echo 'selected'; endif; ?>>
                                        <?php echo e($store->name); ?> (<?php echo e($store->products_count); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-1"></i><?php echo e(__('Apply Filters')); ?>

                        </button>
                        <a href="<?php echo e(URL::current()); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i><?php echo e(__('Clear Filters')); ?>

                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Include Select2 CSS and JS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="<?php echo e(Theme::asset()->url('css/product-filters.css')); ?>" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize Select2 for all filter selects
            $('.filter-select').select2({
                placeholder: function() {
                    return $(this).data('placeholder');
                },
                allowClear: true,
                closeOnSelect: false,
                width: '100%',
                dropdownAutoWidth: true,
                escapeMarkup: function(markup) {
                    return markup;
                }
            });

            // Handle form submission
            $('.bb-product-form-filter').on('submit', function(e) {
                // Show loading state
                $(this).find('.btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i><?php echo e(__("Applying...")); ?>');

                // Remove empty values before submission
                $(this).find('select[multiple]').each(function() {
                    if (!$(this).val() || $(this).val().length === 0) {
                        $(this).prop('disabled', true);
                    }
                });
            });

            // Handle clear filters
            $('.bb-product-filters-top a[href="<?php echo e(URL::current()); ?>"]').on('click', function(e) {
                e.preventDefault();
                $('.filter-select').val(null).trigger('change');
                $('.bb-product-form-filter')[0].reset();
                window.location.href = $(this).attr('href');
            });

            // Auto-submit on filter change (optional)
            $('.filter-select').on('change', function() {
                // Uncomment the line below to enable auto-submit on filter change
                // $('.bb-product-form-filter').submit();
            });
        });
    </script>
<?php endif; ?>
<?php /**PATH D:\laragon\www\muhrak\platform/plugins/ecommerce/resources/views/themes/includes/filters-top.blade.php ENDPATH**/ ?>