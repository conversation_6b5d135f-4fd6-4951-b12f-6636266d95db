@if (EcommerceHelper::hasAnyProductFilters())
    @php
        $dataForFilter = EcommerceHelper::dataForFilter($category ?? null, $request ?? null);
        [$categories, $brands, $tags, $rand, $categoriesRequest, $urlCurrent, $categoryId, $maxFilterPrice, $mainTypes, $stores] = $dataForFilter;
    @endphp

    <div class="bb-product-filters-top">
        <div class="container-fluid">
            <form action="{{ URL::current() }}" data-action="{{ route('public.products') }}" method="GET" class="bb-product-form-filter">
                @include(EcommerceHelper::viewPath('includes.filters.filter-hidden-fields'))

                <div class="row g-3 align-items-end">
                    <!-- Categories Filter -->
                    @if (EcommerceHelper::isEnabledFilterProductsByCategories())
                        <div class="col-md-4">
                            <label class="form-label fw-bold">{{ __('Categories') }}</label>
                            <select name="categories[]" class="form-select filter-select" multiple data-placeholder="{{ __('Select categories...') }}">
                                @foreach ($categories as $category)
                                    <option value="{{ $category->id }}"
                                        @selected(in_array($category->id, (array)request()->input('categories', [])))>
                                        {{ $category->name }}@if(isset($category->products_count)) ({{ $category->products_count }})@endif
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    @endif

                    <!-- Main Types Filter -->
                    @if (EcommerceHelper::isEnabledFilterProductsByMainTypes())
                        <div class="col-md-4">
                            <label class="form-label fw-bold">{{ __('Main Type') }}</label>
                            <select name="main_types[]" class="form-select filter-select" multiple data-placeholder="{{ __('Select main types...') }}">
                                @foreach ($mainTypes as $mainType)
                                    <option value="{{ $mainType->value }}" 
                                        @selected(in_array($mainType->value, (array)request()->input('main_types', [])))>
                                        {{ $mainType->label }} ({{ $mainType->products_count }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    @endif

                    <!-- Stores Filter -->
                    @if (EcommerceHelper::isEnabledFilterProductsByStores())
                        <div class="col-md-4">
                            <label class="form-label fw-bold">{{ __('Stores') }}</label>
                            <select name="stores[]" class="form-select filter-select" multiple data-placeholder="{{ __('Select stores...') }}">
                                @foreach ($stores as $store)
                                    <option value="{{ $store->id }}"
                                        @selected(in_array($store->id, (array)request()->input('stores', [])))>
                                        {{ $store->name }}@if(isset($store->products_count)) ({{ $store->products_count }})@endif
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    @endif
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-1"></i>{{ __('Apply Filters') }}
                        </button>
                        <a href="{{ URL::current() }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>{{ __('Clear Filters') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Include Select2 CSS and JS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="{{ Theme::asset()->url('css/product-filters.css') }}" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize Select2 for all filter selects
            $('.filter-select').select2({
                placeholder: function() {
                    return $(this).data('placeholder');
                },
                allowClear: true,
                closeOnSelect: false,
                width: '100%',
                dropdownAutoWidth: true,
                escapeMarkup: function(markup) {
                    return markup;
                }
            });

            // Handle form submission
            $('.bb-product-form-filter').on('submit', function(e) {
                // Show loading state
                $(this).find('.btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>{{ __("Applying...") }}');

                // Remove empty values before submission
                $(this).find('select[multiple]').each(function() {
                    if (!$(this).val() || $(this).val().length === 0) {
                        $(this).prop('disabled', true);
                    }
                });
            });

            // Handle clear filters
            $('.bb-product-filters-top a[href="{{ URL::current() }}"]').on('click', function(e) {
                e.preventDefault();
                $('.filter-select').val(null).trigger('change');
                $('.bb-product-form-filter')[0].reset();
                window.location.href = $(this).attr('href');
            });

            // Auto-submit on filter change (optional)
            $('.filter-select').on('change', function() {
                // Uncomment the line below to enable auto-submit on filter change
                // $('.bb-product-form-filter').submit();
            });
        });
    </script>
@endif
