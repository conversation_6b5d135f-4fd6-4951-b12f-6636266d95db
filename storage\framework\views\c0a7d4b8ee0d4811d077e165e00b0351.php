<div class="ps-page--shop">
    <div class="container">
        <div class="ps-layout--shop">
            <div class="ps-layout__left">
                <?php echo $__env->make(Theme::getThemeNamespace() . '::views.ecommerce.includes.filters', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
            <div class="ps-layout__right">
                <div class="ps-block--shop-features">
                    <div class="ps-block__header">
                        <h1 class="h1"><?php echo e($category->name); ?></h1>
                    </div>
                    <?php if($category->description): ?>
                        <div class="ps-block__content">
                            <div class="ps-section__content">
                                <?php echo BaseHelper::clean($category->description); ?>

                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.products-list'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>

</div>
<?php /**PATH D:\laragon\www\muhrak\platform\themes/muhrak/views/ecommerce/product-category.blade.php ENDPATH**/ ?>