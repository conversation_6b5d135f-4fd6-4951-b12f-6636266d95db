@if ($mainTypes->isNotEmpty())
    <div class="bb-product-filter">
        <h4 class="bb-product-filter-title">{{ __('Main Type') }}</h4>

        <div class="bb-product-filter-content">
            <ul class="bb-product-filter-items filter-checkbox">
                @foreach ($mainTypes as $mainType)
                    <li class="bb-product-filter-item">
                        <input id="attribute-main-type-{{ $mainType->value }}" type="checkbox" name="main_types[]" value="{{ $mainType->value }}" @checked(in_array($mainType->value, (array)request()->input('main_types', []))) />
                        <label for="attribute-main-type-{{ $mainType->value }}">{{ $mainType->label }} ({{ $mainType->products_count }})</label>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
@endif
