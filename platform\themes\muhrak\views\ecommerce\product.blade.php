@php
    Theme::set('stickyHeader', 'false');
    Theme::set('topHeader', Theme::partial('header-product-page', compact('product')));
    Theme::set('bottomFooter', Theme::partial('footer-product-page', compact('product')));
    Theme::set('pageId', 'product-page');
    Theme::set('headerMobile', Theme::partial('header-mobile-product'));
@endphp

<div class="ps-page--product">
    <div class="container" id="app">
            <div class="ps-page__container">
                <div class="ps-page__left">
                    <div class="ps-product--detail ps-product--fullwidth">
                        <div class="ps-product__header">
                            <div class="ps-product__thumbnail" data-vertical="true">
                                <figure>
                                    <div class="ps-wrapper">
                                        <div class="ps-product__gallery" data-arrow="false">
                                            @foreach ($productImages as $img)
                                                <div class="item">
                                                    <a href="{{ RvMedia::getImageUrl($img) }}">
                                                        <img src="{{ RvMedia::getImageUrl($img) }}" alt="{{ $product->name }}"/>
                                                    </a>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </figure>
                                <div class="ps-product__variants" data-item="4" data-md="4" data-sm="4" data-arrow="true">
                                    @foreach ($productImages as $img)
                                        <div class="item">
                                            <img src="{{ RvMedia::getImageUrl($img, 'thumb') }}" alt="{{ $product->name }}"/>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="ps-product__info">
                                <h1>{{ $product->name }}</h1>
                                 <!-- Model Name Accordion -->
                                <div class="accordion-item-custom">
                                    <div class="info-row accordion-header" data-bs-toggle="collapse" data-bs-target="#modelNameCollapse" aria-expanded="false" style="cursor: pointer;">
                                        <div class="info-label">Model Name</div>
                                        <div class="info-value">
                                            @if ($product->models->count())
                                                {{ $product->models->first()->name }}
                                                <i class="fas fa-external-link-alt ms-1" style="font-size: 0.75rem;"></i>
                                            @endif
                                        </div>
                                        <i class="fas fa-chevron-down dropdown-icon ms-auto accordion-chevron"></i>
                                    </div>
                                    <div class="collapse" id="modelNameCollapse">
                                        <div class="accordion-content">
                                            @if ($product->models->count())
                                                @foreach($product->models->skip(1) as $model)
                                                    <div class="accordion-list-item">
                                                        <i class="fas fa-check-circle text-primary me-2"></i>
                                                        <a href="{{ $model->url }}">{{ $model->name }}</a>
                                                    </div>
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Series Accordion -->
                                <div class="accordion-item-custom">
                                    <div class="info-row accordion-header" data-bs-toggle="collapse" data-bs-target="#seriesCollapse" aria-expanded="false" style="cursor: pointer;">
                                        <div class="info-label">Series</div>
                                        <div class="info-value">
                                            @if ($product->categories->count())
                                                {{ $product->categories->first()->name }}
                                                <i class="fas fa-external-link-alt ms-1" style="font-size: 0.75rem;"></i>
                                            @endif
                                        </div>
                                        <i class="fas fa-chevron-down dropdown-icon ms-auto accordion-chevron"></i>
                                    </div>
                                    <div class="collapse" id="seriesCollapse">
                                        <div class="accordion-content">
                                            @if ($product->categories->count())
                                                @foreach($product->categories->skip(1) as $category)
                                                    <div class="accordion-list-item">
                                                        <i class="fas fa-check-circle text-primary me-2"></i>
                                                        <a href="{{ $category->url }}">{{ $category->name }}</a>
                                                    </div>
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Data Section Accordion -->
                                <div class="data-section">
                                    <div class="data-header accordion-header" data-bs-toggle="collapse" data-bs-target="#dataCollapse" aria-expanded="false" style="cursor: pointer;">
                                        <span>Data</span>
                                        <span>Catalog-Numalliance-CNC Wire Bender Robomac e-Motion</span>
                                        <i class="fas fa-chevron-down dropdown-icon accordion-chevron"></i>
                                    </div>
                                    <div class="collapse" id="dataCollapse">
                                        <div class="accordion-content">
                                            <div class="accordion-list-item">
                                                Catalog-Numalliance-CNC Wire Bender Robomac e-Motion (Current)
                                            </div>
                                            <div class="accordion-list-item">
                                                Technical Specifications Sheet
                                            </div>
                                            <div class="accordion-list-item">

                                                Product Images Gallery
                                            </div>
                                            <div class="accordion-list-item">
                                                Operation Manual Video
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                @if (is_plugin_active('marketplace') && $product->store_id)
                                <!-- Manufacturer Section -->
                                <div class="manufacturer-section">
                                    <h2 class="manufacturer-title">Manufacturer information</h2>

                                    <a href="{{ $product->store->url }}" class="manufacturer-info">
                                        <div class="manufacturer-logo">
                                            <img src="{{ RvMedia::getImageUrl($product->store->logo, 'full', false, RvMedia::getDefaultImage()) }}" alt="{{ $product->store->name }}">
                                        </div>
                                        <div class="manufacturer-details">
                                            <h5>
                                                {{ $product->store->name }}
                                                <i class="fas fa-check-circle verified-badge"></i>
                                                <i class="fas fa-chevron-right ms-1" style="color: #5f6368; font-size: 0.75rem;"></i>
                                            </h5>
                                            <p class="manufacturer-description">
                                                {!! BaseHelper::clean($product->store->description) !!}
                                            </p>
                                        </div>
                                    </a>

                                    <!-- Action Buttons -->
                                    <div class="action-buttons">
                                        <button class="btn btn-inquiry">
                                            <i class="fas fa-comment-dots me-2"></i>Inquiry
                                        </button>
                                        <button class="btn btn-order">
                                            How to order
                                        </button>
                                    </div>

                                    <!-- Problem Section -->
                                    <div class="problem-section">
                                        Problem with product info?
                                        <a href="#" class="problem-link">
                                            Update request <i class="fas fa-chevron-right ms-1"></i>
                                        </a>
                                    </div>

                                </div>
                                @endif
                            </div>
                        </div>
                        <div class="ps-product__content ps-tab-root">
                                <div class="product-description-table">
                                    {!! BaseHelper::clean($product->description) !!}
                                </div>
                                <div class="ck-content">
                                    {!! BaseHelper::clean($product->content) !!}
                                </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="ps-section--default">
                <div class="ps-section__header">
                    <h3>{{ __('Related products') }}</h3>
                </div>
                <div class="ps-section__content">
                    <div class="ps-carousel--responsive owl-slider"
                         data-owl-auto="true"
                         data-owl-loop="false"
                         data-owl-speed="10000"
                         data-owl-gap="10"
                         data-owl-nav="false"
                         data-owl-dots="true"
                         data-owl-item="5"
                         data-owl-item-xs="2"
                         data-owl-item-sm="3"
                         data-owl-item-md="3"
                         data-owl-item-lg="4"
                         data-owl-item-xl="5"
                         data-owl-duration="1000"
                         data-owl-mousedrag="on"
                    >
                        @foreach($product->store->products()->take(7)->get() as $product)
                            <div class="ps-product">
                                {!! Theme::partial('product-item', ['product' => $product]) !!}
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
    </div>
</div>
