<?php

use Bo<PERSON>ble\Theme\Facades\Theme;
use Illuminate\Support\Facades\Route;
use Theme\Muhrak\Http\Controllers\MuhrakController;

Theme::registerRoutes(function (): void {
    Route::group(['controller' => MuhrakController::class], function (): void {
        Route::group(['prefix' => 'ajax', 'as' => 'public.ajax.'], function (): void {
            Route::get('products', 'ajaxGetProducts')
                ->name('products');

            Route::get('cart', 'ajaxCart')
                ->name('cart');

            Route::get('search-products', 'ajaxSearchProducts')
                ->name('search-products');

            Route::post('send-download-app-links', 'ajaxSendDownloadAppLinks')
                ->name('send-download-app-links');

            Route::get('products-by-collection/{id}', 'ajaxGetProductsByCollection')
                ->name('products-by-collection')
                ->wherePrimary<PERSON>ey();

            Route::get('products-by-category/{id}', 'ajaxGetProductsByCategory')
                ->name('products-by-category')
                ->wherePrimaryKey();

            Route::get('search-categories', 'ajaxSearchCategories')
                ->name('search-categories');

            Route::get('search-stores', 'ajaxSearchStores')
                ->name('search-stores');
        });
    });
});

Theme::routes();
