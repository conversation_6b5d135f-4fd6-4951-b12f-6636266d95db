<?php if(($attributes = $attributes->where('attribute_set_id', $set->id)) && $attributes->isNotEmpty()): ?>
    <div class="bb-product-filter-attribute-item">
        <h4 class="bb-product-filter-title"><?php echo e($set->title); ?></h4>

        <div class="bb-product-filter-content">
            <figure class="visual-swatches-wrapper widget--colors widget-filter-item" data-type="visual">
                <div class="widget__content ps-custom-scrollbar">
                    <div class="attribute-values">
                        <ul class="visual-swatch color-swatch">
                            <?php $__currentLoopData = $attributes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attribute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li data-slug="<?php echo e($attribute->slug); ?>"
                                    title="<?php echo e($attribute->title); ?>">
                                    <div class="custom-checkbox">
                                        <label>
                                            <input class="form-control product-filter-item" type="checkbox" name="attributes[<?php echo e($set->slug); ?>][]" value="<?php echo e($attribute->id); ?>" <?php echo e(in_array($attribute->id, $selected) ? 'checked' : ''); ?>>
                            <span style="<?php echo e($attribute->getAttributeStyle()); ?>"></span>
                                        </label>
                                    </div>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
            </figure>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\muhrak\platform\themes/muhrak/views/ecommerce/attributes/_layouts-filter/visual.blade.php ENDPATH**/ ?>