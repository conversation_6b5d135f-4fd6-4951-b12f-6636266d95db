<div class="ps-page--shop">
    <?php if(theme_option('show_featured_brands_on_products_page', 'yes') == 'yes'): ?>
    <div class="container">
    <div class="mt-40">
            <div class="ps-shop-brand ps-carousel--responsive owl-slider"
                data-owl-auto="true"
                data-owl-loop="false"
                data-owl-speed="10000"
                data-owl-gap="0"
                data-owl-nav="false"
                data-owl-dots="true"
                data-owl-item="7"
                data-owl-item-xs="2"
                data-owl-item-sm="2"
                data-owl-item-md="3"
                data-owl-item-lg="4"
                data-owl-item-xl="6"
                data-owl-duration="1000"
                data-owl-mousedrag="on"
            >
                <?php $__currentLoopData = get_featured_brands(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($brand->website): ?>
                        <a href="<?php echo e($brand->website); ?>">
                    <?php endif; ?>
                    <img src="<?php echo e(RvMedia::getImageUrl($brand->logo, null, false, RvMedia::getDefaultImage())); ?>" alt="<?php echo e($brand->name); ?>" loading="lazy"/>
                    <?php if($brand->website): ?>
                        </a>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
        <?php endif; ?>

    <!-- Top Filters Section -->
    <?php echo $__env->make(EcommerceHelper::viewPath('includes.filters-top'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Products Section -->
    <div class="container">
        <div class="ps-layout--shop-no-sidebar">
            <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.products-list'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<?php /**PATH D:\laragon\www\muhrak\platform\themes/muhrak/views/ecommerce/products.blade.php ENDPATH**/ ?>