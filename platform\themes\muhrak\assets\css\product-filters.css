/* Product Filters Top Layout */
.bb-product-filters-top {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 20px 0;
    margin-bottom: 20px;
}

.bb-product-filters-top .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
}

.filter-select {
    min-height: 45px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    font-size: 14px;
}

/* Select2 Styling */
.select2-container {
    width: 100% !important;
}

.select2-container--default .select2-selection--multiple {
    min-height: 45px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 4px 8px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #0d6efd;
    border: 1px solid #0d6efd;
    color: white;
    border-radius: 0.25rem;
    padding: 2px 8px;
    margin: 2px;
    font-size: 13px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white;
    margin-right: 5px;
    font-size: 16px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #ccc;
}

.select2-container--default .select2-selection--multiple .select2-search__field {
    margin-top: 4px;
}

.select2-dropdown {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #0d6efd;
}

/* Layout without sidebar */
.ps-layout--shop-no-sidebar {
    width: 100%;
    max-width: none;
}

/* Filter buttons */
.bb-product-filters-top .btn {
    padding: 10px 20px;
    font-weight: 500;
    border-radius: 0.375rem;
}

.bb-product-filters-top .btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.bb-product-filters-top .btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.bb-product-filters-top .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .bb-product-filters-top {
        padding: 15px 0;
    }
    
    .bb-product-filters-top .row.g-3 {
        gap: 1rem !important;
    }
    
    .bb-product-filters-top .col-md-4 {
        margin-bottom: 15px;
    }
    
    .select2-container--default .select2-selection--multiple {
        min-height: 40px;
    }
    
    .bb-product-filters-top .btn {
        padding: 8px 16px;
        font-size: 14px;
        width: 100%;
        margin-bottom: 10px;
    }
}

/* Products grid adjustments */
.ps-shopping-product .row {
    margin: 0 -10px;
}

.ps-shopping-product .row > div {
    padding: 0 10px;
    margin-bottom: 20px;
}

/* Filter form styling */
.bb-product-form-filter {
    margin: 0;
}

/* Loading state */
.filter-select:disabled {
    background-color: #e9ecef;
    opacity: 0.65;
}

/* Custom scrollbar for select2 dropdown */
.select2-results__options {
    max-height: 200px;
    overflow-y: auto;
}

.select2-results__options::-webkit-scrollbar {
    width: 6px;
}

.select2-results__options::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.select2-results__options::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.select2-results__options::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation for filter changes */
.bb-product-filters-top {
    transition: all 0.3s ease;
}

.filter-select {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-select:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Clear filters link styling */
.bb-product-filters-top a {
    text-decoration: none;
}

.bb-product-filters-top a:hover {
    text-decoration: none;
}

/* Filter section spacing */
.bb-product-filters-top .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
}

@media (min-width: 1200px) {
    .bb-product-filters-top .container-fluid {
        padding-left: 30px;
        padding-right: 30px;
    }
}

/* Select2 search field styling */
.select2-search--inline .select2-search__field {
    border: none;
    outline: none;
    box-shadow: none;
    background: transparent;
}

/* Placeholder styling */
.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
    color: #6c757d;
    font-style: italic;
}

/* Results styling */
.select2-container--default .select2-results__option {
    padding: 8px 12px;
    font-size: 14px;
}

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #e9ecef;
    color: #495057;
}

/* No results message */
.select2-container--default .select2-results__option--nodata {
    color: #6c757d;
    font-style: italic;
}
